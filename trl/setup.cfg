[metadata]
name = trl
version = 0.21.0.dev0
description = Train transformer language models with reinforcement learning.
long_description = file: README.md
long_description_content_type = text/markdown
author = <PERSON><PERSON><PERSON>
author_email = <EMAIL>
url = https://github.com/huggingface/trl
keywords = transformers, huggingface, language modeling, post-training, rlhf, sft, dpo, grpo
license_file = LICENSE
classifiers =
    Development Status :: 2 - Pre-Alpha
    Intended Audience :: Developers
    Intended Audience :: Science/Research
    Natural Language :: English
    Operating System :: OS Independent
    Programming Language :: Python :: 3
    Programming Language :: Python :: 3.9
    Programming Language :: Python :: 3.10
    Programming Language :: Python :: 3.11
    Programming Language :: Python :: 3.12
    Programming Language :: Python :: 3.13

[options]
packages = find_namespace:
python_requires = >=3.9
include_package_data = True
install_requires =
    accelerate>=1.4.0
    datasets>=3.0.0
    transformers>=4.53.2

[options.packages.find]
exclude =
    tests*

[options.extras_require]
bco =
    scikit-learn
    joblib
deepspeed =
    deepspeed>=0.14.4
diffusers =
    diffusers>=0.18.0
judges =
    openai>=1.23.2
    llm-blender>=0.0.2
liger =
    liger-kernel>=0.5.9
peft =
    peft>=0.8.0
quantization =
    bitsandbytes
scikit =
    scikit-learn
test =
    parameterized
    pytest-cov
    pytest-rerunfailures
    pytest-xdist
    pytest
vllm =
    # vLLM package does not yet support Python 3.13. These constraints can be lifted once support is added:
    # see https://github.com/vllm-project/vllm/pull/13164
    vllm>=0.10.0; python_version < "3.13"
    fastapi; python_version < "3.13"
    pydantic; python_version < "3.13"
    requests; python_version < "3.13"
    uvicorn; python_version < "3.13"

vlm =
    Pillow
    torchvision
    num2words
dev =
    %(bco)s
    %(deepspeed)s
    %(diffusers)s
    %(judges)s
    %(liger)s
    %(peft)s
    %(quantization)s
    %(scikit)s
    %(test)s
    %(vlm)s

[options.entry_points]
console_scripts =
    trl = trl.cli:main

[coverage:run]
branch = True
