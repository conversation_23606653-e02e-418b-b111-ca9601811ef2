name: Tests

on:
  push:
    branches: [ main ]
  pull_request:
    paths:
      # Run only when relevant files are modified
      - ".github/**.yml"
      - "examples/**.py"
      - "scripts/**.py"
      - "tests/**.py"
      - "trl/**.py"
      - "setup.py"

env:
  TQDM_DISABLE: 1
  CI_SLACK_CHANNEL: ${{ secrets.CI_PUSH_MAIN_CHANNEL }}

jobs:
  check_code_quality:
    name: Check code quality
    runs-on: ubuntu-latest
    if: github.event.pull_request.draft == false
    steps:
      - uses: actions/checkout@v4
      - name: Set up Python 3.12
        uses: actions/setup-python@v5
        with:
          python-version: 3.12
      - uses: pre-commit/action@v3.0.1
        with:
          extra_args: --all-files

  tests:
    name: Tests
    strategy:
      matrix:
        python-version: ['3.9', '3.10', '3.11', '3.12', '3.13']
      fail-fast: false
    runs-on:
      group: aws-g4dn-2xlarge
    container:
      image: pytorch/pytorch:2.6.0-cuda12.6-cudnn9-devel
      options: --gpus all
    defaults:
      run:
        shell: bash
    if: github.event.pull_request.draft == false
    steps:
      - name: Git checkout
        uses: actions/checkout@v4

      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v5
        with:
          python-version: ${{ matrix.python-version }}

      - name: Install Make and Git
        run: |
          apt-get update && apt-get install -y make git curl

      - name: Install uv
        run: |
          curl -LsSf https://astral.sh/uv/install.sh | sh

      - name: Create Python virtual environment
        run: |
          uv venv
          uv pip install --upgrade setuptools wheel

      - name: Install dependencies
        run: |
          source .venv/bin/activate
          uv pip install ".[dev]"

      - name: Test with pytest
        run: |
          source .venv/bin/activate
          make test

      - name: Post to Slack
        if: github.ref == 'refs/heads/main' && always()  # Check if the branch is main
        uses: huggingface/hf-workflows/.github/actions/post-slack@main
        with:
          slack_channel: ${{ env.CI_SLACK_CHANNEL }}
          title: Results with Python ${{ matrix.python-version }} and latest dependencies
          status: ${{ job.status }}
          slack_token: ${{ secrets.SLACK_CIFEEDBACK_BOT_TOKEN }}

  tests_dev:
    name: Tests with dev dependencies
    runs-on:
      group: aws-g4dn-2xlarge
    container:
      image: pytorch/pytorch:2.6.0-cuda12.6-cudnn9-devel
      options: --gpus all
    defaults:
      run:
        shell: bash
    if: github.event.pull_request.draft == false
    steps:
      - name: Git checkout
        uses: actions/checkout@v4

      - name: Set up Python 3.12
        uses: actions/setup-python@v5
        with:
          python-version: '3.12'

      - name: Install Make and Git
        run: |
          apt-get update && apt-get install -y make git curl

      - name: Install uv
        run: |
          curl -LsSf https://astral.sh/uv/install.sh | sh

      - name: Create Python virtual environment
        run: |
          uv venv
          uv pip install --upgrade setuptools wheel

      - name: Install dependencies
        run: |
          source .venv/bin/activate
          uv pip install ".[dev]"
          uv pip install -U git+https://github.com/huggingface/accelerate.git
          uv pip install -U git+https://github.com/huggingface/datasets.git
          uv pip install -U git+https://github.com/huggingface/transformers.git
          

      - name: Test with pytest
        run: |
          source .venv/bin/activate
          make test

      - name: Post to Slack
        if: github.ref == 'refs/heads/main' && always()  # Check if the branch is main
        uses: huggingface/hf-workflows/.github/actions/post-slack@main
        with:
          slack_channel: ${{ env.CI_SLACK_CHANNEL }}
          title: Results with Python 3.12 and dev dependencies
          status: ${{ job.status }}
          slack_token: ${{ secrets.SLACK_CIFEEDBACK_BOT_TOKEN }}

  tests_wo_optional_deps:
    name: Tests without optional dependencies
    runs-on:
      group: aws-g4dn-2xlarge
    container:
      image: pytorch/pytorch:2.6.0-cuda12.6-cudnn9-devel
      options: --gpus all
    defaults:
      run:
        shell: bash
    if: github.event.pull_request.draft == false
    steps:
      - name: Git checkout
        uses: actions/checkout@v4

      - name: Set up Python 3.12
        uses: actions/setup-python@v5
        with:
          python-version: '3.12'

      - name: Install Make and Git
        run: |
          apt-get update && apt-get install -y make git curl

      - name: Install uv
        run: |
          curl -LsSf https://astral.sh/uv/install.sh | sh

      - name: Create Python virtual environment
        run: |
          uv venv
          uv pip install --upgrade setuptools wheel

      - name: Install dependencies
        run: |
          source .venv/bin/activate
          uv pip install ".[test]"

      - name: Test with pytest
        run: |
          source .venv/bin/activate
          make test

      - name: Post to Slack
        if: github.ref == 'refs/heads/main' && always()  # Check if the branch is main
        uses: huggingface/hf-workflows/.github/actions/post-slack@main
        with:
          slack_channel: ${{ env.CI_SLACK_CHANNEL }}
          title: Results with Python 3.12 without optional dependencies
          status: ${{ job.status }}
          slack_token: ${{ secrets.SLACK_CIFEEDBACK_BOT_TOKEN }}

  tests_min_versions:
    name: Tests with minimum versions
    runs-on:
      group: aws-g4dn-2xlarge
    container:
      image: pytorch/pytorch:2.6.0-cuda12.6-cudnn9-devel
      options: --gpus all
    defaults:
      run:
        shell: bash
    if: github.event.pull_request.draft == false
    steps:
      - name: Git checkout
        uses: actions/checkout@v4

      - name: Set up Python 3.12
        uses: actions/setup-python@v5
        with:
          python-version: '3.12'

      - name: Install Make and Git
        run: |
          apt-get update && apt-get install -y make git curl

      - name: Install uv
        run: |
          curl -LsSf https://astral.sh/uv/install.sh | sh

      - name: Create Python virtual environment
        run: |
          uv venv
          uv pip install --upgrade setuptools wheel

      - name: Install dependencies
        run: |
          source .venv/bin/activate
          uv pip install ".[dev]"
          uv pip install accelerate==1.4.0
          uv pip install datasets==3.0.0
          uv pip install transformers==4.53.2

      - name: Test with pytest
        run: |
          source .venv/bin/activate
          make test

      - name: Post to Slack
        if: github.ref == 'refs/heads/main' && always()  # Check if the branch is main
        uses: huggingface/hf-workflows/.github/actions/post-slack@main
        with:
          slack_channel: ${{ env.CI_SLACK_CHANNEL }}
          title: Results with Python 3.12 and minimum dependencies versions
          status: ${{ job.status }}
          slack_token: ${{ secrets.SLACK_CIFEEDBACK_BOT_TOKEN }}