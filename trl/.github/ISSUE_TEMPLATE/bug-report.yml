name: "\U0001F41B Bug Report"
description: Submit a bug report to help us improve TRL
labels: [ "bug" ]
body:
  - type: markdown
    attributes:
      value: |
        Thanks for taking the time to fill out this bug report! 🤗

        🚩 If it is your first time submitting, be sure to check our [bug report guidelines](https://github.com/huggingface/trl/blob/main/CONTRIBUTING.md#did-you-find-a-bug)

  - type: textarea
    id: reproduction
    validations:
      required: true
    attributes:
      label: Reproduction
      description: |
        Please provide a code sample that reproduces the problem you ran into. It can be a Colab link or just a code snippet.
        If you have code snippets, error messages, stack traces please provide them here as well.
        Important! Use code tags to correctly format your code. See https://help.github.com/en/github/writing-on-github/creating-and-highlighting-code-blocks#syntax-highlighting
        Do not use screenshots, as they are hard to read and (more importantly) don't allow others to copy-and-paste your code.

      value: |
        ```python
        from trl import ...

        ```

        outputs:

        ```
        Traceback (most recent call last):
          File "example.py", line 42, in <module>
            ...
        ```

  - type: textarea
    id: system-info
    attributes:
      label: System Info
      description: |
        Please provide information about your system: platform, Python version, PyTorch version, Transformers version, devices, TRL version, ...
        You can get this information by running `trl env` in your terminal.

      placeholder: Copy-paste the output of `trl env`
    validations:
      required: true

  - type: checkboxes
    id: terms
    attributes:
      label: Checklist
      description: |
        Before submitting, please confirm that you've completed each of the following.
        If an item doesn't apply to your issue, check it anyway to show you've reviewed it.
      options:
        - label: "I have checked that my issue isn't already filed (see [open issues](https://github.com/huggingface/trl/issues?q=is%3Aissue))"
          required: true
        - label: "I have included my system information"
          required: true
        - label: "Any code provided is minimal, complete, and reproducible ([more on MREs](https://docs.github.com/en/get-started/writing-on-github/working-with-advanced-formatting/creating-and-highlighting-code-blocks))"
          required: true
        - label: "Any code provided is properly formatted in code blocks, (no screenshot, [more on code blocks](https://docs.github.com/en/get-started/writing-on-github/working-with-advanced-formatting/creating-and-highlighting-code-blocks))"
          required: true
        - label: "Any traceback provided is complete"
          required: true
