import numpy as np
import random
import torch
def set_random_seed(seed: int = 42):
    """
    Set the random seed for reproducibility across Python, NumPy, and PyTorch.

    Parameters:
        seed (int): The seed value to use.
    """
    # Set the seed for Python's built-in random module
    random.seed(seed)

    # Set the seed for NumPy
    np.random.seed(seed)

    # Set the seed for PyTorch
    torch.manual_seed(seed)

    if torch.cuda.is_available():
        torch.cuda.manual_seed_all(seed)

    # Ensure deterministic behavior in cuDNN (may impact performance)
    torch.backends.cudnn.deterministic = True
    torch.backends.cudnn.benchmark = False

set_random_seed(42)

import os
os.environ["WANDB_API_KEY"] = "PUT_YOUR_KEY_HERE"
os.environ["WANDB_PROJECT"] = "GRPO-Qwen-0.5-Instruct"

!wandb login
!pip install flash-attn
!pip install trl

import os
import hashlib
import tarfile
import requests
import re
import wandb
from torch.nn.utils.rnn import pad_sequence
from datasets import load_dataset, Dataset
from transformers import AutoTokenizer, AutoModelForCausalLM, Trainer, TrainingArguments, DataCollatorForLanguageModeling, TrainerCallback, PreTrainedTokenizerBase
from trl import GRPOConfig, GRPOTrainer

SYSTEM_PROMPT = """
Respond in the following format:

<reasoning>
...
</reasoning>
<answer>
...
</answer>
"""

def extract_answer_from_model_output(text):
    """
    Extracts the value from the last <answer> tag in the text.
    Returns None if no valid answer is found.
    """
    # Split on <answer> and take everything after the last occurrence
    parts = text.split("<answer>")
    if len(parts) < 2:  # No <answer> tag found
        return None

    last_part = parts[-1]

    # Extract content up to </answer>
    if "</answer>" not in last_part:
        return None

    answer = last_part.split("</answer>")[0].strip()
    return None if answer == "..." else answer

def extract_answer_from_dataset(text):
    """
    Extracts the answer from the dataset.
    The dataset separates the answer using the '####' delimiter.
    """
    if "####" not in text:
        return None
    return text.split("####")[1].strip()

def prepare_dataset(split="train"):
    """Load and prepare the GSM8K dataset for training."""
    data = load_dataset('openai/gsm8k', 'main')[split]
    formatted_data = []

    for example in data:
        formatted_example = {
            "prompt": [
                {"role": "system", "content": SYSTEM_PROMPT},
                {"role": "user", "content": example["question"]}
            ],
            "answer": extract_answer_from_dataset(example["answer"])
        }
        formatted_data.append(formatted_example)

    return formatted_data

def build_prompt(messages):
    """
    Build a single prompt string from a list of messages.
    Each message is expected to be a dictionary with 'role' and 'content' keys.
    This function concatenates all message contents, preserving the training format.
    """
    return "\n".join([msg["content"].strip() for msg in messages])

class ChatDataCollator:
    def __init__(self, tokenizer: PreTrainedTokenizerBase, max_length: int = 512):
        self.tokenizer = tokenizer
        self.max_length = max_length

    def __call__(self, batch):
        inputs = []
        labels = []
        for example in batch:
            # Here we assume the last message is the target (assistant's output)
            prompt = build_prompt(example["messages"][:-1])
            target = example["messages"][-1]["content"]

            # Concatenate prompt and target (add a newline between them)
            full_text = prompt + "\n" + target
            tokenized = self.tokenizer(full_text, truncation=True, max_length=self.max_length)
            input_ids = torch.tensor(tokenized["input_ids"])
            inputs.append(input_ids)
            # You can choose to set labels equal to input_ids, or modify as needed.
            labels.append(input_ids)

        inputs_padded = pad_sequence(inputs, batch_first=True, padding_value=self.tokenizer.pad_token_id)
        labels_padded = pad_sequence(labels, batch_first=True, padding_value=-100)
        return {"input_ids": inputs_padded, "labels": labels_padded}

def download_and_extract_cot_archive(url, extract_path):
    """Download and extract the CoT archive if not already done."""
    archive_path = os.path.join(extract_path, "cot.tar.gz")
    if not os.path.exists(extract_path):
        os.makedirs(extract_path, exist_ok=True)
    if not os.path.exists(archive_path):
        print("Downloading CoT archive...")
        r = requests.get(url, stream=True)
        with open(archive_path, "wb") as f:
            for chunk in r.iter_content(chunk_size=8192):
                if chunk:
                    f.write(chunk)
    # Extract the archive if not already extracted.
    extract_dir = os.path.join(extract_path, "cot_files")
    if not os.path.exists(extract_dir):
        print("Extracting CoT archive...")
        with tarfile.open(archive_path, "r:gz") as tar:
            tar.extractall(path=extract_dir)
    return extract_dir

def prepare_sft_dataset(num_examples=500):
    """
    Prepare SFT examples in the chat format required by your custom collator.
    Each example will be a dict with a "messages" key.
    """
    SYSTEM_PROMPT = """
    Respond in the following format:
    <reasoning>
    ...
    </reasoning>
    <answer>
    ...
    </answer>
    """
    cot_url = "https://github.com/aburkov/theLMbook/releases/download/v1.0.0/cot.tar.gz"
    extract_dir = download_and_extract_cot_archive(cot_url, extract_path="cot_archive")
    data = load_dataset('openai/gsm8k', 'main')["train"]

    sft_examples = []
    for example in data:
        question = example["question"].strip()
        # Compute the filename based on the SHA-256 hash of the question.
        filename = hashlib.sha256(question.encode()).hexdigest() + ".txt"
        file_path = os.path.join(extract_dir, filename)

        if os.path.exists(file_path):
            with open(file_path, "r", encoding="utf-8") as f:
                cot_output = f.read().strip()

            # Build the chat-format example.
            formatted_example = {
                "messages": [
                    {"role": "system", "content": SYSTEM_PROMPT},
                    {"role": "user", "content": question},
                    {"role": "assistant", "content": cot_output}
                ]
            }
            sft_examples.append(formatted_example)

        if len(sft_examples) >= num_examples:
            break

    if len(sft_examples) < num_examples:
        print(f"Warning: Only found {len(sft_examples)} SFT examples.")
    else:
        print(f"Prepared {len(sft_examples)} SFT examples.")

    return sft_examples

def _extract_last_number(text):
    """
    Extracts the last number from text if it's properly separated.
    The number must be at the end and separated by space or = sign.
    Ignores $ and % signs.
    Returns None if no valid number is found.
    """
    import re

    # Remove $ and % signs
    text = text.replace('$', '').replace('%', '')

    # Look for numbers that are:
    # - preceded by space or = or start of string (via \b or ^)
    # - followed by end of string or space
    pattern = r'(?:^|\s|=)\s*(-?\d*\.?\d+)\s*$'
    match = re.search(pattern, text)
    return float(match.group(1)) if match else None

def _extract_single_number(text):
    """
    Extracts a single number from text if exactly one exists,
    otherwise returns None.
    """
    import re
    numbers = re.findall(r'-?\d*\.?\d+', text)
    return float(numbers[0]) if len(numbers) == 1 else None

def evaluate_model(model, tokenizer, eval_examples, device):
    """Evaluates the model on a set of examples and prints detailed results."""
    model.eval()
    correct = 0
    total = len(eval_examples)
    print("\n" + "="*50)
    print("EVALUATION ON", total, "EXAMPLES")
    print("="*50)
    for example in eval_examples:
        # Build the full prompt using the same method as training.
        full_prompt = build_prompt(example["prompt"])
        expected = example["answer"]
        # Tokenize the full prompt and generate a response from the model.
        inputs = tokenizer.encode(full_prompt, return_tensors="pt").to(device)
        outputs = model.generate(
            inputs,
            max_length=512,
            temperature=0.7,
            num_return_sequences=1
        )
        response = tokenizer.decode(outputs[0], skip_special_tokens=True)
        # Extract the predicted answer from the model output.
        try:
            predicted = extract_answer_from_model_output(response)
            # Check correctness in multiple ways
            if predicted == expected:  # First try exact match
                is_correct = True
            else:
                # Try single number
                pred_num = _extract_single_number(str(predicted))
                exp_num = _extract_single_number(str(expected))
                if pred_num is not None and exp_num is not None and pred_num == exp_num:
                    is_correct = True
                else:
                    # Try last number
                    pred_num = _extract_last_number(str(predicted))
                    exp_num = _extract_last_number(str(expected))
                    is_correct = (pred_num is not None and exp_num is not None and
                                pred_num == exp_num)

            if is_correct:
                correct += 1
            # Print details of the evaluation.
            print("\nPrompt:")
            print(full_prompt)
            print("\nExpected Answer:")
            print(expected)
            print("\nExtracted Answer:")
            print(predicted)
            print("\nFull Generated Response:")
            print(response)
            print("\nCorrect:", "✓" if is_correct else "✗")
            print("-"*50)
        except Exception as e:
            print("\nFailed to parse model output for prompt:")
            print(full_prompt)
            print("Error:", e)
            print("-"*50)
    accuracy = (correct / total) * 100
    print(f"\nAccuracy: {accuracy:.2f}% ({correct}/{total})")
    print("="*50)
    model.train()
    return accuracy

# Define a custom callback class for evaluation.
class EvalCallback(TrainerCallback):
    def __init__(self, model, tokenizer, eval_examples, device):
        self.model = model
        self.tokenizer = tokenizer
        self.eval_examples = eval_examples
        self.device = device

    def on_train_begin(self, args, state, control, **kwargs):
        return control

    def on_epoch_begin(self, args, state, control, **kwargs):
        return control

    def on_step_end(self, args, state, control, **kwargs):
        if state.global_step % args.eval_steps == 0:
            print(f"\nEvaluating at step {state.global_step}:")
            evaluate_model(self.model, self.tokenizer, self.eval_examples, self.device)
        return control

    def on_epoch_end(self, args, state, control, **kwargs):
        return control

    def on_train_end(self, args, state, control, **kwargs):
        return control

def correctness_reward(prompts, completions, answer, **kwargs):
    """
    Assigns a reward based on the correctness of the model's answer.
    Also logs detailed metrics about the response.
    """

    responses = [completion[0]['content'] for completion in completions]
    extracted = [extract_answer_from_model_output(r) for r in responses]

    rewards = []
    for r, a in zip(extracted, answer):
        if r == a:  # Exact match case
            rewards.append(2.0)
        else:
            # Try numeric equivalence
            r_num = _extract_single_number(str(r))
            a_num = _extract_single_number(str(a))
            if r_num is not None and a_num is not None and r_num == a_num:
                rewards.append(1.5)
            else:
                rewards.append(0.0)

    # Log completion lengths
    completion_lengths = [len(response.split()) for response in responses]
    return rewards

def format_reward(completions, **kwargs):
    """
    Assigns a reward for adhering to the desired XML format.
    Also logs detailed format compliance metrics.
    """
    responses = [completion[0]['content'] for completion in completions]
    rewards = []
    format_scores = []

    for response in responses:
        score = 0.0
        if "<reasoning>" in response: score += 0.20
        if "</reasoning>" in response: score += 0.20
        if "<answer>" in response: score += 0.20
        if "</answer>" in response: score += 0.20
        rewards.append(score)
        format_scores.append(score)

    return rewards

def main():
    # Determine the device: use GPU if available, else fallback to CPU.
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"Using device: {device}")

    # Model configuration.
    model_name = "Qwen/Qwen2.5-0.5B-Instruct"
    output_dir = "math_solver_model"

    # Load the pre-trained model on CPU first, then move to GPU.
    model = AutoModelForCausalLM.from_pretrained(
        model_name,
        torch_dtype=torch.bfloat16,
        attn_implementation="flash_attention_2",
        device_map="auto"
    )
    model = model.to(device)

    tokenizer = AutoTokenizer.from_pretrained(model_name)
    tokenizer.pad_token = tokenizer.eos_token

    examples_to_use_for_evaluation = 30

    ##############################
    # Step 0. PRE-SFT EVALUATION #
    ##############################

    # Immediately after loading the model, tokenizer, and determining the device (i.e. in main(), after model/tokenizer load)
    # Insert this code block to prepare evaluation examples and run evaluation BEFORE SFT fine-tuning.
    all_data = prepare_dataset("train")
    random.shuffle(all_data)
    eval_data = all_data[:examples_to_use_for_evaluation]

    print("\nInitial model evaluation BEFORE SFT:")
    pre_sft_accuracy = evaluate_model(model, tokenizer, eval_data, device)
    print(f"Pre-SFT Accuracy: {pre_sft_accuracy:.2f}%")

    ###########################
    # Step 1: SFT Fine-Tuning #
    ###########################
    print("\nPreparing SFT dataset...")
    sft_dataset = prepare_sft_dataset(num_examples=500)

    sft_training_args = TrainingArguments(
        output_dir="sft_output",
        overwrite_output_dir=True,
        num_train_epochs=1,
        per_device_train_batch_size=2,
        learning_rate=5e-5,
        save_steps=100,
        bf16=True,
        gradient_checkpointing=True,
        remove_unused_columns=False,
        report_to=[]
    )

    print("\nStarting SFT fine-tuning...")
    sft_trainer = Trainer(
        model=model,
        args=sft_training_args,
        train_dataset=sft_dataset,
        data_collator=ChatDataCollator(tokenizer)
    )
    sft_trainer.train()

    # Evaluate the model after SFT
    post_sft_accuracy = evaluate_model(model, tokenizer, eval_data, device)
    print(f"\nPost-SFT Accuracy: {post_sft_accuracy:.2f}%")
    print(f"\nImprovement after SFT: {post_sft_accuracy - pre_sft_accuracy:.2f}%")

    ##########################
    # Step 2: RL Fine-Tuning #
    ##########################
    # Prepare RL dataset (using the same prepare_dataset function).
    all_data = prepare_dataset("train")
    random.shuffle(all_data)

    train_data = all_data[examples_to_use_for_evaluation:]

    # Define training configuration using GRPOConfig.
    training_args = GRPOConfig(
        output_dir=output_dir,
        learning_rate=5e-6,
        logging_steps=5,
        eval_steps=50,
        bf16=True,
        per_device_train_batch_size=8,
        gradient_accumulation_steps=4,
        max_steps=500,
        num_train_epochs=1,
        save_steps=100,
        max_grad_norm=0.1,
        num_generations=8,
        max_completion_length=300,
        report_to=["wandb"]
    )

    wandb.init(
        config={
            "model_name": model_name,
            "learning_rate": training_args.learning_rate,
            "batch_size": training_args.per_device_train_batch_size,
            "num_epochs": training_args.num_train_epochs
        }
    )

    # Create the trainer with our reward functions.
    trainer = GRPOTrainer(
        model=model,
        processing_class=tokenizer,
        reward_funcs=[format_reward, correctness_reward],
        args=training_args,
        train_dataset=train_data,
    )

    trainer.add_callback(EvalCallback(model, tokenizer, eval_data, device))

    # Start training
    trainer.train()

    print("\nFinal model evaluation AFTER GRPO:")
    post_grpo_accuracy = evaluate_model(model, tokenizer, eval_data, device)
    print(f"Post-GRPO Accuracy: {post_grpo_accuracy:.2f}%")
    print(f"Improvement after GRPO: {post_grpo_accuracy - pre_sft_accuracy:.2f}%")

    ###########################
    # Step 3. SAVE THE MODEL       #
    ###########################

    print("Saving GRPO fine-tuned model to 'grpo_finetuned_model'...")
    model.save_pretrained("grpo_finetuned_model")
    # Save the tokenizer:
    tokenizer.save_pretrained("grpo_finetuned_model")

    # Close wandb
    wandb.finish()


if __name__ == "__main__":
    main()


###########################
# Step 4. LOAD AND TEST MODEL  #
###########################

from transformers import AutoTokenizer, AutoModelForCausalLM

def main():

    # Determine the device: use GPU if available, else fallback to CPU.
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"Using device: {device}")

    # Load the saved model and tokenizer
    saved_model_path = "grpo_finetuned_model"
    loaded_model = AutoModelForCausalLM.from_pretrained(saved_model_path, torch_dtype=torch.bfloat16, device_map="auto")
    loaded_model = loaded_model.to(device)
    loaded_tokenizer = AutoTokenizer.from_pretrained(saved_model_path)
    loaded_tokenizer.pad_token = loaded_tokenizer.eos_token

    prompts_to_test = [
        "How much is 1+1?",
        "I have 3 apples, my friend eats one and I give 2 to my sister, how many apples do I have now?",
        "Solve the quation 6x + 4 = 40"
    ]

    for prompt in prompts_to_test:
        # Prepare the prompt using the chat format supported by the Qwen model.
        test_messages = [
            {"role": "system", "content": SYSTEM_PROMPT},
            {"role": "user", "content": prompt}
        ]
        test_prompt = build_prompt(test_messages)

        # Tokenize the prompt and generate a response.
        test_input_ids = loaded_tokenizer.encode(test_prompt, return_tensors="pt").to(device)
        test_output_ids = loaded_model.generate(
            test_input_ids,
            max_length=256,
            temperature=1.0,
            num_return_sequences=1
        )
        test_response = loaded_tokenizer.decode(test_output_ids[0], skip_special_tokens=True)

        # Print the test prompt and the model's response.
        print("\nTest Prompt:")
        print(test_prompt)
        print("\nModel Response:")
        print(test_response)

if __name__ == "__main__":
    main()