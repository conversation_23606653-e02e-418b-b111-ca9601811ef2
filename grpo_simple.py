# ============================================================================
# GRPO (Generalized Reward-based Policy Optimization) 完整算法伪代码
# 基于 TRL 库的完整实现
# ============================================================================

def GRPO_Training(model, reference_model, reward_model, dataset, config):
    """
    完整的 GRPO 训练算法
    
    Args:
        model: 当前策略模型 π_θ
        reference_model: 参考模型 π_ref (可选)
        reward_model: 奖励模型 R(prompt, completion)
        dataset: 训练数据集 (prompts)
        config: GRPO 配置参数
    """
    
    optimizer = AdamW(model.parameters(), lr=config.learning_rate)
    
    for epoch in range(config.num_epochs):
        for batch in DataLoader(dataset, batch_size=config.batch_size):
            
            # ========================================
            # 第一阶段：生成和评分
            # ========================================
            episodes = generate_and_score_completions(
                model, batch, reward_model, config
            )
            
            # ========================================
            # 第二阶段：GRPO 损失计算和更新
            # ========================================
            loss = compute_grpo_loss(
                model, reference_model, episodes, config
            )
            
            # 反向传播和参数更新
            loss.backward()
            clip_grad_norm_(model.parameters(), config.max_grad_norm)
            optimizer.step()
            optimizer.zero_grad()


def generate_and_score_completions(model, batch, reward_model, config):
    """
    生成候选文本并计算奖励
    """
    episodes = []
    
    # 处理输入 prompts
    prompts = batch["prompts"]
    
    # 应用聊天模板和 tokenization
    prompt_inputs = tokenizer(
        prompts, 
        padding=True, 
        return_tensors="pt"
    )
    
    # 生成多个候选回答
    with torch.no_grad():
        for _ in range(config.num_generations):
            # 使用当前策略生成文本
            completions = model.generate(
                prompt_inputs["input_ids"],
                max_length=config.max_completion_length,
                temperature=config.temperature,
                top_p=config.top_p,
                do_sample=True
            )
            
            # 计算奖励
            for i, (prompt, completion) in enumerate(zip(prompts, completions)):
                reward = reward_model(prompt, completion)
                
                episode = {
                    "prompt": prompt,
                    "completion": completion,
                    "reward": reward,
                    "prompt_ids": prompt_inputs["input_ids"][i],
                    "completion_ids": completions[i]
                }
                episodes.append(episode)
    
    # 按 prompt 组归一化奖励
    episodes = normalize_rewards_per_group(episodes)
    
    # 计算优势值 (advantages)
    for episode in episodes:
        episode["advantage"] = episode["reward"]  # 简化版，实际可能有基线
    
    return episodes


def compute_grpo_loss(model, reference_model, episodes, config):
    """
    计算 GRPO 损失函数
    """
    total_loss = 0.0
    
    for batch_episodes in batch_episodes_generator(episodes, config.micro_batch_size):
        
        # 准备批次数据
        prompt_ids = [ep["prompt_ids"] for ep in batch_episodes]
        completion_ids = [ep["completion_ids"] for ep in batch_episodes]
        advantages = [ep["advantage"] for ep in batch_episodes]
        
        # 拼接 prompt 和 completion
        input_ids = [torch.cat([p, c]) for p, c in zip(prompt_ids, completion_ids)]
        input_ids = pad_sequences(input_ids, pad_token_id)
        
        # 创建 completion mask (只对生成的 token 计算损失)
        completion_mask = create_completion_mask(prompt_ids, completion_ids)
        
        # ========================================
        # 计算当前策略的 log 概率
        # ========================================
        logits = model(input_ids)
        per_token_logps = get_per_token_logps(logits, input_ids, completion_mask)
        
        # ========================================
        # 获取旧策略的 log 概率 (用于重要性采样)
        # ========================================
        if hasattr(batch_episodes[0], "old_per_token_logps"):
            old_per_token_logps = [ep["old_per_token_logps"] for ep in batch_episodes]
        else:
            # 如果是第一次迭代，使用当前策略作为旧策略
            old_per_token_logps = per_token_logps.detach()
        
        # ========================================
        # 重要性采样权重计算
        # ========================================
        log_ratio = per_token_logps - old_per_token_logps
        
        if config.importance_sampling_level == "token":
            log_importance_weights = log_ratio
        elif config.importance_sampling_level == "sequence":
            # 序列级别：对整个序列求平均
            log_importance_weights = (log_ratio * completion_mask).sum(-1) / completion_mask.sum(-1).clamp(min=1.0)
            log_importance_weights = log_importance_weights.unsqueeze(-1)
        
        # ========================================
        # GRPO 核心：双边裁剪机制
        # ========================================
        coef_1 = torch.exp(log_importance_weights)  # 重要性权重
        coef_2 = torch.clamp(
            coef_1, 
            1 - config.epsilon_low,   # 下界裁剪
            1 + config.epsilon_high   # 上界裁剪
        )
        
        # 可选：单边裁剪 (delta 参数)
        if config.delta is not None:
            coef_1 = torch.clamp(coef_1, max=config.delta)
        
        # ========================================
        # GRPO 损失计算 (PPO 风格的双边裁剪)
        # ========================================
        advantages_tensor = torch.tensor(advantages).unsqueeze(1)
        
        per_token_loss1 = coef_1 * advantages_tensor
        per_token_loss2 = coef_2 * advantages_tensor
        per_token_loss = -torch.min(per_token_loss1, per_token_loss2)
        
        # ========================================
        # 可选：KL 正则化
        # ========================================
        if config.beta != 0.0 and reference_model is not None:
            # 计算与参考模型的 KL 散度
            ref_logits = reference_model(input_ids)
            ref_per_token_logps = get_per_token_logps(ref_logits, input_ids, completion_mask)
            
            # KL 散度计算
            per_token_kl = (
                torch.exp(ref_per_token_logps - per_token_logps) 
                - (ref_per_token_logps - per_token_logps) 
                - 1
            )
            
            # 添加 KL 正则化项
            per_token_loss = per_token_loss + config.beta * per_token_kl
        
        # ========================================
        # 可选：高熵 token 掩码
        # ========================================
        if config.top_entropy_quantile < 1.0:
            entropies = compute_entropy_from_logits(logits)
            entropy_mask = get_high_entropy_mask(
                entropies, completion_mask, 1 - config.top_entropy_quantile
            )
            per_token_loss = per_token_loss * entropy_mask
        
        # ========================================
        # 损失聚合
        # ========================================
        if config.loss_type == "grpo":
            # 序列平均
            batch_loss = (
                (per_token_loss * completion_mask).sum(-1) / completion_mask.sum(-1).clamp(min=1.0)
            ).mean()
        elif config.loss_type == "bnpo":
            # 全局平均
            batch_loss = (per_token_loss * completion_mask).sum() / completion_mask.sum().clamp(min=1.0)
        elif config.loss_type == "dr_grpo":
            # 密度比率版本
            batch_loss = (per_token_loss * completion_mask).sum() / (
                per_token_loss.size(0) * config.max_completion_length
            )
        
        total_loss += batch_loss
    
    return total_loss


# ============================================================================
# 辅助函数
# ============================================================================

def normalize_rewards_per_group(episodes):
    """按 prompt 组归一化奖励"""
    groups = defaultdict(list)
    for episode in episodes:
        groups[episode["prompt"]].append(episode)
    
    for group_episodes in groups.values():
        rewards = [ep["reward"] for ep in group_episodes]
        mean_reward = np.mean(rewards)
        std_reward = np.std(rewards) + 1e-8
        
        for ep in group_episodes:
            ep["reward"] = (ep["reward"] - mean_reward) / std_reward
    
    return episodes


def get_per_token_logps(logits, input_ids, completion_mask):
    """计算每个 token 的 log 概率"""
    log_probs = torch.log_softmax(logits, dim=-1)
    per_token_logps = torch.gather(
        log_probs[:, :-1], 
        dim=-1, 
        index=input_ids[:, 1:].unsqueeze(-1)
    ).squeeze(-1)
    return per_token_logps * completion_mask


def get_high_entropy_mask(entropies, completion_mask, quantile):
    """获取高熵 token 的掩码"""
    valid_entropies = entropies[completion_mask]
    threshold = torch.quantile(valid_entropies, quantile)
    return (entropies >= threshold).float()


def compute_entropy_from_logits(logits):
    """从 logits 计算熵"""
    probs = torch.softmax(logits, dim=-1)
    log_probs = torch.log_softmax(logits, dim=-1)
    entropy = -(probs * log_probs).sum(dim=-1)
    return entropy


# ============================================================================
# 主训练循环
# ============================================================================

def main():
    # 初始化模型、数据、配置
    model = load_model(config.model_name)
    reference_model = load_model(config.model_name) if config.beta > 0 else None
    reward_model = load_reward_model(config.reward_model_name)
    dataset = load_dataset(config.dataset_name)
    
    # 开始 GRPO 训练
    GRPO_Training(model, reference_model, reward_model, dataset, config)